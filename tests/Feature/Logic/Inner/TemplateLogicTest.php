<?php

namespace Tests\Feature\Logic\Inner;

use App\Constants\CommonConst;
use App\ErrCode\BaseErr;
use App\ErrCode\FieldErr;
use App\ErrCode\TemplateErr;
use App\Exceptions\ErrException;
use App\Logic\Inner\TemplateLogic;
use App\Models\BusinessFieldModel;
use App\Models\BusinessModel;
use App\Models\BusinessTemplateFieldModel;
use App\Models\BusinessTemplateModel;
use Laravel\Lumen\Testing\DatabaseTransactions;
use Tests\TestCase;

class TemplateLogicTest extends TestCase
{
    use DatabaseTransactions;

    protected $templateLogic;

    protected function setUp(): void
    {
        parent::setUp();
        $this->templateLogic = TemplateLogic::getInstance();
    }

    /**
     * Test list method with successful pagination
     */
    public function testListSuccess()
    {
        // Create test business
        $businessId = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com',
            'state' => 1,
            'create_time' => time()
        ]);

        // Create test templates
        $templates = [
            [
                'business_id' => $businessId,
                'template_name' => 'Template 1',
                'biz_type' => 'input',
                'state' => CommonConst::STATE_ACTIVE,
                'is_deleted' => CommonConst::NOT_DELETED,
                'create_time' => time()
            ],
            [
                'business_id' => $businessId,
                'template_name' => 'Template 2',
                'biz_type' => 'output',
                'state' => CommonConst::STATE_ACTIVE,
                'is_deleted' => CommonConst::NOT_DELETED,
                'create_time' => time()
            ]
        ];
        BusinessTemplateModel::query()->insert($templates);

        $params = [
            'page' => 1,
            'pageSize' => 10,
            'businessId' => $businessId
        ];

        $result = $this->templateLogic->list($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('page', $result);
        $this->assertArrayHasKey('pageSize', $result);
        $this->assertArrayHasKey('list', $result);
        $this->assertArrayHasKey('isEnd', $result);
        $this->assertEquals(2, $result['page']);
        $this->assertEquals(10, $result['pageSize']);
        $this->assertCount(2, $result['list']);
        $this->assertTrue($result['isEnd']);
    }

    /**
     * Test list method with filters
     */
    public function testListWithFilters()
    {
        // Create test business
        $businessId = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com',
            'state' => 1,
            'create_time' => time()
        ]);

        // Create test templates with different states and types
        BusinessTemplateModel::query()->insert([
            'business_id' => $businessId,
            'template_name' => 'Active Input Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        BusinessTemplateModel::query()->insert([
            'business_id' => $businessId,
            'template_name' => 'Disabled Output Template',
            'biz_type' => 'output',
            'state' => CommonConst::STATE_DISABLE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        // Test filter by bizType
        $params = [
            'page' => 1,
            'pageSize' => 10,
            'businessId' => $businessId,
            'bizType' => 'input'
        ];

        $result = $this->templateLogic->list($params);
        $this->assertCount(1, $result['list']);
        $this->assertEquals('input', $result['list'][0]['bizType']);

        // Test filter by state
        $params = [
            'page' => 1,
            'pageSize' => 10,
            'businessId' => $businessId,
            'state' => CommonConst::STATE_DISABLE
        ];

        $result = $this->templateLogic->list($params);
        $this->assertCount(1, $result['list']);
        $this->assertEquals(CommonConst::STATE_DISABLE, $result['list'][0]['state']);
    }

    /**
     * Test list method with empty results
     */
    public function testListEmpty()
    {
        $params = [
            'page' => 1,
            'pageSize' => 10,
            'businessId' => 999999
        ];

        $result = $this->templateLogic->list($params);

        $this->assertIsArray($result);
        $this->assertEmpty($result['list']);
        $this->assertTrue($result['isEnd']);
    }

    /**
     * Test list method with boundary conditions
     */
    public function testListBoundaryConditions()
    {
        // Test with page = 0 (should be converted to 1)
        $params = [
            'page' => 0,
            'pageSize' => 10
        ];

        $result = $this->templateLogic->list($params);
        $this->assertEquals(2, $result['page']); // page + 1

        // Test with negative page (should be converted to 1)
        $params = [
            'page' => -5,
            'pageSize' => 10
        ];

        $result = $this->templateLogic->list($params);
        $this->assertEquals(2, $result['page']); // page + 1

        // Test with pageSize = 0
        $params = [
            'page' => 1,
            'pageSize' => 0
        ];

        $result = $this->templateLogic->list($params);
        $this->assertEquals(0, $result['pageSize']);
    }

    /**
     * Test create method success
     */
    public function testCreateSuccess()
    {
        // Create test business
        $businessId = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com',
            'state' => 1,
            'create_time' => time()
        ]);

        $params = [
            'businessId' => $businessId,
            'templateName' => 'Test Template',
            'bizType' => 'input'
        ];

        $result = $this->templateLogic->create($params);

        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Verify template was created
        $template = BusinessTemplateModel::query()
            ->where('business_id', $businessId)
            ->where('template_name', 'Test Template')
            ->first();

        $this->assertNotNull($template);
        $this->assertEquals('input', $template->biz_type);
        $this->assertEquals(CommonConst::STATE_ACTIVE, $template->state);
        $this->assertEquals(CommonConst::NOT_DELETED, $template->is_deleted);
    }

    /**
     * Test create method with non-existent business
     */
    public function testCreateWithNonExistentBusiness()
    {
        $params = [
            'businessId' => 999999,
            'templateName' => 'Test Template',
            'bizType' => 'input'
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->create($params);
    }

    /**
     * Test create method with duplicate template name
     */
    public function testCreateWithDuplicateTemplateName()
    {
        // Create test business
        $businessId = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com',
            'state' => 1,
            'create_time' => time()
        ]);

        // Create existing template
        BusinessTemplateModel::query()->insert([
            'business_id' => $businessId,
            'template_name' => 'Existing Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $params = [
            'businessId' => $businessId,
            'templateName' => 'Existing Template',
            'bizType' => 'output'
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(TemplateErr::TEMPLATE_EXISTS[0]);
        $this->expectExceptionMessage('模板已存在');

        $this->templateLogic->create($params);
    }

    /**
     * Test edit method success
     */
    public function testEditSuccess()
    {
        // Create test template
        $templateId = BusinessTemplateModel::query()->insertGetId([
            'business_id' => 1,
            'template_name' => 'Original Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $params = [
            'id' => $templateId,
            'templateName' => 'Updated Template'
        ];

        $result = $this->templateLogic->edit($params);

        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Verify template was updated
        $template = BusinessTemplateModel::query()->find($templateId);
        $this->assertEquals('Updated Template', $template->template_name);
    }

    /**
     * Test edit method with non-existent template
     */
    public function testEditWithNonExistentTemplate()
    {
        $params = [
            'id' => 999999,
            'templateName' => 'Updated Template'
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->edit($params);
    }
}
